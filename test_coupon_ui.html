<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coupon UI Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .discount { display: flex; justify-content: space-between; margin: 10px 0; }
        .total { display: flex; justify-content: space-between; font-weight: bold; margin: 10px 0; }
        .hidden { display: none; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Coupon UI Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Percentage Discount</h2>
        <div id="discountsection-1" class="hidden">
            <div class="discount">
                <span id="discounttag-1">Discount</span>
                <span id="discountamountdisplay-1">-USD 0.00</span>
            </div>
            <p id="discountnote-1">Discount applied</p>
        </div>
        <div class="total">
            <span>Total:</span>
            <span id="totalamount-1">USD 100.00</span>
        </div>
        <button onclick="testPercentageDiscount()">Test 10% Off Forever</button>
    </div>

    <div class="test-section">
        <h2>Test 2: Fixed Amount Discount</h2>
        <div id="discountsection-2" class="hidden">
            <div class="discount">
                <span id="discounttag-2">Discount</span>
                <span id="discountamountdisplay-2">-USD 0.00</span>
            </div>
            <p id="discountnote-2">Discount applied</p>
        </div>
        <div class="total">
            <span>Total:</span>
            <span id="totalamount-2">USD 100.00</span>
        </div>
        <button onclick="testFixedDiscount()">Test $15 Off Once</button>
    </div>

    <div class="test-section">
        <h2>Test 3: Repeating Discount</h2>
        <div id="discountsection-3" class="hidden">
            <div class="discount">
                <span id="discounttag-3">Discount</span>
                <span id="discountamountdisplay-3">-USD 0.00</span>
            </div>
            <p id="discountnote-3">Discount applied</p>
        </div>
        <div class="total">
            <span>Total:</span>
            <span id="totalamount-3">USD 100.00</span>
        </div>
        <button onclick="testRepeatingDiscount()">Test 20% Off for 3 Months</button>
    </div>

    <script>
        // Mock DOM utility functions
        const DOM = {
            getelement: (id) => document.getElementById(id + '-' + window.currentInstance),
            setelement: (id, content) => {
                const element = DOM.getelement(id);
                if (element) element.textContent = content;
            },
            toggleelement: (id, show) => {
                const element = DOM.getelement(id);
                if (element) {
                    element.style.display = show ? 'block' : 'none';
                    element.classList.toggle('hidden', !show);
                }
            }
        };

        // Mock updateUIFromServerResponse function
        const updateUIFromServerResponse = (data) => {
            if (data.uistate !== "error") {
                DOM.toggleelement("discountsection", data.showsections.discountsection);
                
                if (data.showsections.discountsection) {
                    if (data.couponname) {
                        DOM.setelement("discounttag", data.couponname);
                    }
                    if (data.discountamount && data.currency) {
                        DOM.setelement("discountamountdisplay", `-${data.currency} ${data.discountamount}`);
                    }
                    if (data.discountamount && data.discountvalue) {
                        let note = data.coupontype === "percentoff"
                            ? `${data.discountvalue}% off`
                            : `${data.currency} ${data.discountvalue} off`;
                        
                        // Add duration information if available
                        if (data.couponduration) {
                            if (data.couponduration === "repeating" && data.coupondurationmonths) {
                                note += ` for ${data.coupondurationmonths} months`;
                            } else if (data.couponduration !== "once") {
                                note += ` ${data.couponduration}`;
                            }
                        }
                        
                        DOM.setelement("discountnote", note);
                    }
                }
                
                if (data.status && data.currency) {
                    const totalamount = DOM.getelement("totalamount");
                    if (totalamount) {
                        totalamount.textContent = `${data.currency} ${parseFloat(data.status).toFixed(2)}`;
                    }
                }
            }
        };

        function testPercentageDiscount() {
            window.currentInstance = 1;
            const mockData = {
                status: "90.00",
                couponname: "SAVE10",
                coupontype: "percentoff",
                discountvalue: 10,
                currency: "USD",
                discountamount: "10.00",
                couponduration: "forever",
                uistate: "paid",
                showsections: {
                    discountsection: true
                }
            };
            updateUIFromServerResponse(mockData);
        }

        function testFixedDiscount() {
            window.currentInstance = 2;
            const mockData = {
                status: "85.00",
                couponname: "SAVE15",
                coupontype: "amountoff",
                discountvalue: 15,
                currency: "USD",
                discountamount: "15.00",
                couponduration: "once",
                uistate: "paid",
                showsections: {
                    discountsection: true
                }
            };
            updateUIFromServerResponse(mockData);
        }

        function testRepeatingDiscount() {
            window.currentInstance = 3;
            const mockData = {
                status: "80.00",
                couponname: "SAVE20",
                coupontype: "percentoff",
                discountvalue: 20,
                currency: "USD",
                discountamount: "20.00",
                couponduration: "repeating",
                coupondurationmonths: 3,
                uistate: "paid",
                showsections: {
                    discountsection: true
                }
            };
            updateUIFromServerResponse(mockData);
        }
    </script>
</body>
</html>
