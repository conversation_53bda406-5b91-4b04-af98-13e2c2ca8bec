<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Total Due Today Alignment Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .comparison { display: flex; gap: 30px; }
        .section { flex: 1; border: 1px solid #ccc; padding: 20px; }
        .section h3 { margin-top: 0; }
        
        /* Before - Misaligned */
        .before .total-misaligned {
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .before .total-misaligned .name {
            font-size: 1.125rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .before .total-misaligned .price {
            font-size: 1.125rem;
            font-weight: 700;
            text-align: right;
        }
        
        /* After - Properly Aligned */
        .after .total {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 0.875rem 0 !important;
            border-top: 2px solid #333 !important;
            margin-top: 0.875rem !important;
            margin-bottom: 0 !important;
        }
        
        .after .total .details {
            flex: 1;
            margin: 0;
        }
        
        .after .total .details .name {
            font-size: 1.125rem !important;
            font-weight: 700 !important;
            margin: 0 !important;
            line-height: 1.2 !important;
        }
        
        .after .total .price {
            text-align: right !important;
            margin: 0;
        }
        
        .after .total .price div {
            font-size: 1.125rem !important;
            font-weight: 700 !important;
            margin: 0 !important;
            line-height: 1.2 !important;
        }
        
        /* Common styles */
        .line-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 3px 0;
        }
        
        .subtotal {
            border-top: 1px solid #eee;
            padding-top: 8px;
            font-weight: bold;
        }
        
        .discount {
            color: #00a86b;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
        }
        
        .wrong {
            background-color: #ffe6e6;
        }
        
        .correct {
            background-color: #e6ffe6;
        }
    </style>
</head>
<body>
    <h1>🎯 Total Due Today Alignment Fix</h1>
    
    <div class="comparison">
        <div class="section before wrong">
            <h3>❌ Before - Misaligned</h3>
            
            <div class="line-item">
                <span>Enrolment in course course 1</span>
                <span>$70.00</span>
            </div>
            <div class="line-item">
                <span>Enrolment in course course 1<br><small>Billed every 2 months</small></span>
                <span>$50.00</span>
            </div>
            
            <div class="line-item subtotal">
                <span>Subtotal</span>
                <span>$120.00</span>
            </div>
            
            <div class="line-item discount">
                <span>kk<br><small>7% off</small></span>
                <span>-$8.40</span>
            </div>
            
            <!-- Misaligned total section -->
            <div class="total-misaligned">
                <div class="name">Total due today</div>
                <div class="price">$111.60</div>
            </div>
            
            <p><strong>Problem:</strong> Text and value are not on the same line</p>
        </div>
        
        <div class="section after correct">
            <h3>✅ After - Properly Aligned</h3>
            
            <div class="line-item">
                <span>Enrolment in course course 1</span>
                <span>$70.00</span>
            </div>
            <div class="line-item">
                <span>Enrolment in course course 1<br><small>Billed every 2 months</small></span>
                <span>$50.00</span>
            </div>
            
            <div class="line-item subtotal">
                <span>Subtotal</span>
                <span>$120.00</span>
            </div>
            
            <div class="line-item discount">
                <span>kk<br><small>7% off</small></span>
                <span>-$8.40</span>
            </div>
            
            <!-- Properly aligned total section -->
            <div class="total">
                <div class="details">
                    <div class="name">Total due today</div>
                </div>
                <div class="price">
                    <div>$111.60</div>
                </div>
            </div>
            
            <p><strong>Fixed:</strong> Text and value are perfectly aligned on the same line</p>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding: 20px; background: #f5f5f5; border-radius: 5px;">
        <h2>🔧 CSS Fix Applied</h2>
        <p><strong>Key Changes:</strong></p>
        <ul>
            <li>✅ Added <code>display: flex</code> to ensure horizontal layout</li>
            <li>✅ Used <code>justify-content: space-between</code> for proper spacing</li>
            <li>✅ Added <code>align-items: center</code> for vertical alignment</li>
            <li>✅ Set consistent margins and padding</li>
            <li>✅ Used <code>!important</code> to override any conflicting styles</li>
        </ul>
        
        <h3>CSS Code Added:</h3>
        <pre style="background: #333; color: #fff; padding: 15px; border-radius: 5px; overflow-x: auto;">
.total {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.875rem 0 !important;
    border-top: 2px solid #333 !important;
    margin-top: 0.875rem !important;
    margin-bottom: 0 !important;
}

.total .details .name {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

.total .price div {
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}</pre>
        
        <p><strong>Result:</strong> "Total due today" and its value are now perfectly aligned on the same line! ✨</p>
    </div>
</body>
</html>
