define(["core/ajax"],function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);const{call:o}=n.default,s=e=>{const t=new Map;return{getelement(n){const o=`${n}-${e}`;return t.has(o)||t.set(o,document.getElementById(o)),t.get(o)},setelement(e,t){const n=this.getelement(e);n&&(n.innerHTML=t)},toggleelement(e,t){const n=this.getelement(e);n&&(n.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,n,o=(t?"0.7":"1")){const s=this.getelement(e);s&&(s.disabled=t,s.textContent=n,s.style.opacity=o,s.style.cursor=t?"not-allowed":"pointer")}}};return{stripe_payment_pro:function(e,t,n,r,c,a,i){const u=s(n);if(void 0===window.Stripe)return;const l=(e,t,n)=>{let o;switch(n){case"error":o="red";break;case"success":o="green";break;default:o="blue"}u.setelement(e,`<p style="color: ${o}; font-weight: bold;">${t}</p>`),u.toggleelement(e,!0)},d=e=>{u.setelement(e,""),u.toggleelement(e,!1)};[{id:"apply",event:"click",handler:async e=>{e.preventDefault();const s=u.getelement("coupon"),r=s?.value.trim();if(!r)return l("showmessage",c,"error"),void u.focuselement("coupon");u.setbutton("apply",!0,a);try{const e=await((e,t)=>o([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(r,n);if(void 0===e?.status)throw new Error("Invalid server response");t=r,u.toggleelement("coupon",!1),u.toggleelement("apply",!1),(e=>{if(e.message?l("showmessage",e.message,"error"===e.uistate?"error":"success"):d("showmessage"),u.toggleelement("enrolbutton","paid"===e.uistate),u.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(u.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection){if(e.couponname&&u.setelement("discounttag",e.couponname),e.discountamount){const t="USD"===e.currency?"$":e.currency+" ";u.setelement("discountamountdisplay",`-${t}${e.discountamount}`)}if(e.discountamount&&e.discountvalue){const t="USD"===e.currency?"$":e.currency+" ";let n="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${t}${e.discountvalue} off`;e.couponduration&&("repeating"===e.couponduration&&e.coupondurationmonths?n+=` for ${e.coupondurationmonths} months`:"once"!==e.couponduration&&(n+=` ${e.couponduration}`)),u.setelement("discountnote",n)}}if(e.status&&e.currency){const t=`${"USD"===e.currency?"$":e.currency+" "}${parseFloat(e.status).toFixed(2)}`,n=u.getelement("mainprice");n&&(n.textContent=t);const o=u.getelement("totalamount");o&&(o.textContent=t)}}})(e)}catch(e){l("showmessage",e.message||"Coupon validation failed","error"),u.focuselement("coupon")}}},{id:"enrolbutton",event:"click",handler:async()=>{if(u.getelement("enrolbutton")){d("paymentresponse"),u.setbutton("enrolbutton",!0,r);try{const s=await((e,t,n)=>o([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:n}}])[0])(e,t,n);s.error?.message?l("paymentresponse",s.error.message,"error"):"success"===s.status&&s.redirecturl?window.location.href=s.redirecturl:l("paymentresponse","Unknown error occurred during payment.","error")}catch(e){l("paymentresponse",e.message,"error")}finally{u.toggleelement("enrolbutton",!1)}}}}].forEach(({id:e,event:t,handler:n})=>{const o=u.getelement(e);o&&o.addEventListener(t,n)})},initCouponSettings:()=>{console.log("Coupon settings initialized");const e=document.querySelector(".table-responsive");if(e&&!document.querySelector("#coupon-search")){const t=document.createElement("div");t.style.marginBottom="15px",t.innerHTML='\n            <input type="text" id="coupon-search" placeholder="Search coupons..."\n                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">\n        ',e.parentNode.insertBefore(t,e),document.getElementById("coupon-search").addEventListener("input",e=>{const t=e.target.value.toLowerCase();document.querySelectorAll(".table tbody tr").forEach(e=>{e.style.display=e.textContent.toLowerCase().includes(t)?"":"none"})})}}}});
