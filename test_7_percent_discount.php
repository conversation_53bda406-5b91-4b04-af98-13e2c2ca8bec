<?php
/**
 * Test script for 7% discount calculation
 * 
 * This script verifies the 7% discount calculation matches Stripe:
 * - Initial payment: $70
 * - Recurring payment: $50 every 2 months  
 * - Subtotal: $120 (70 + 50)
 * - 7% discount: $8.40 (7% of $120)
 * - Expected result: $111.60 total due today
 */

echo "=== 7% Discount Calculation Test ===\n\n";

// Test scenario values
$sign_up_fee = 70.00;
$renewal_fee = 50.00;
$subtotal = $sign_up_fee + $renewal_fee; // $120
$discount_percentage = 7; // 7%

echo "Scenario:\n";
echo "- Sign-up fee (initial): $" . number_format($sign_up_fee, 2) . "\n";
echo "- Renewal fee (recurring): $" . number_format($renewal_fee, 2) . "\n";
echo "- Subtotal: $" . number_format($subtotal, 2) . "\n";
echo "- Discount: {$discount_percentage}% off\n\n";

// Calculate discount amount
$discount_amount = $subtotal * ($discount_percentage / 100);
$total_due_today = $subtotal - $discount_amount;

echo "Calculation:\n";
echo "- Discount amount: $" . number_format($subtotal, 2) . " × {$discount_percentage}% = $" . number_format($discount_amount, 2) . "\n";
echo "- Total due today: $" . number_format($subtotal, 2) . " - $" . number_format($discount_amount, 2) . " = $" . number_format($total_due_today, 2) . "\n\n";

echo "Expected Stripe result: $111.60\n";
echo "Our calculation result: $" . number_format($total_due_today, 2) . "\n";

if (abs($total_due_today - 111.60) < 0.01) {
    echo "✅ MATCH! Our calculation matches Stripe.\n\n";
} else {
    echo "❌ MISMATCH! Our calculation doesn't match Stripe.\n\n";
}

// Test the format_float function behavior
echo "=== Format Testing ===\n";
echo "Raw calculation: " . $total_due_today . "\n";
echo "Formatted (2 decimals): " . number_format($total_due_today, 2) . "\n";
echo "PHP format_float equivalent: " . sprintf("%.2f", $total_due_today) . "\n\n";

// Test edge cases
echo "=== Edge Cases ===\n";

// Test with different percentages
$test_percentages = [5, 10, 15, 25, 50];
foreach ($test_percentages as $pct) {
    $test_discount = $subtotal * ($pct / 100);
    $test_total = $subtotal - $test_discount;
    echo "{$pct}% off: $" . number_format($subtotal, 2) . " - $" . number_format($test_discount, 2) . " = $" . number_format($test_total, 2) . "\n";
}

echo "\n=== Summary ===\n";
echo "✓ Discount should be calculated as: subtotal × percentage\n";
echo "✓ For 7% off $120: $120 × 0.07 = $8.40 discount\n";
echo "✓ Total due today: $120 - $8.40 = $111.60\n";
echo "✓ This matches Stripe's calculation exactly!\n";
?>
