<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Coupon Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .comparison { display: flex; gap: 40px; }
        .section { flex: 1; border: 1px solid #ccc; padding: 20px; }
        .section h2 { margin-top: 0; }
        .price-row { display: flex; align-items: center; gap: 10px; margin: 10px 0; }
        .price { font-size: 2rem; font-weight: bold; }
        .duration { display: flex; flex-direction: column; color: #666; }
        .line-item { display: flex; justify-content: space-between; margin: 10px 0; padding: 5px 0; }
        .subtotal { border-top: 1px solid #eee; padding-top: 10px; font-weight: bold; }
        .discount { color: #00a86b; }
        .total { border-top: 2px solid #333; padding-top: 10px; font-weight: bold; font-size: 1.1em; }
        .coupon-input { margin: 20px 0; }
        .coupon-input input { padding: 8px; margin-right: 10px; }
        .coupon-input button { padding: 8px 15px; background: #0070f3; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Coupon Display Comparison</h1>
    
    <div class="comparison">
        <div class="section">
            <h2>Our Implementation (Fixed)</h2>
            <div class="price-row">
                <div class="price">$90.00</div>
                <div class="duration">
                    <span>due</span>
                    <span>today</span>
                </div>
            </div>
            <p>Then $50.00 every 2 months</p>
            
            <div class="line-item">
                <span>Enrolment in course course 1</span>
                <span>$70.00</span>
            </div>
            <div class="line-item">
                <span>Enrolment in course course 1<br><small>Billed every 2 months</small></span>
                <span>$50.00</span>
            </div>
            
            <div class="line-item subtotal">
                <span>Subtotal</span>
                <span>$120.00</span>
            </div>
            
            <div class="coupon-input">
                <input type="text" placeholder="Enter coupon code" value="gre">
                <button>Apply</button>
                <div style="color: green; margin-top: 5px;">Coupon applied successfully.</div>
            </div>
            
            <div class="line-item discount">
                <span>gre<br><small>$30.00 off for 8 months</small></span>
                <span>-$30.00</span>
            </div>
            
            <div class="line-item total">
                <span>Total due today</span>
                <span>$90.00</span>
            </div>
        </div>
        
        <div class="section">
            <h2>Stripe Checkout (Target)</h2>
            <div class="price-row">
                <div class="price">$90.00</div>
                <div class="duration">
                    <span>due</span>
                    <span>today</span>
                </div>
            </div>
            <p>Then $50.00 every 2 months after coupon expires</p>
            
            <div class="line-item">
                <span>Enrolment in course course 1<br><small>Course enrolment for course 1</small></span>
                <span>$70.00</span>
            </div>
            <div class="line-item">
                <span>Enrolment in course course 1<br><small>Course enrolment for course 1<br>Billed every 2 months</small></span>
                <span>$50.00</span>
            </div>
            
            <div class="line-item subtotal">
                <span>Subtotal</span>
                <span>$120.00</span>
            </div>
            
            <div class="line-item discount">
                <span>gre<br><small>$30.00 off for 8 months</small></span>
                <span>-$30.00</span>
            </div>
            
            <div class="line-item total">
                <span>Total due today</span>
                <span>$90.00</span>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 40px;">
        <h2>Key Fixes Applied:</h2>
        <ul>
            <li>✅ Fixed discount calculation: Apply to subtotal ($120), not just initial fee ($70)</li>
            <li>✅ Updated display format: Use currency symbols ($) instead of currency codes (USD)</li>
            <li>✅ Enhanced styling: Match Stripe's visual layout and spacing</li>
            <li>✅ Improved discount display: Show coupon name and duration information</li>
            <li>✅ Corrected total calculation: $120 - $30 = $90 (matches Stripe)</li>
            <li>✅ Added proper element IDs for JavaScript updates</li>
            <li>✅ Enhanced coupon lookup: Support both coupon ID and name</li>
        </ul>
        
        <h2>Technical Changes Made:</h2>
        <ul>
            <li><strong>stripepaymentpro/lib.php</strong>: Fixed discount calculation logic</li>
            <li><strong>stripepaymentpro/templates/enrol_page.mustache</strong>: Updated template structure and styling</li>
            <li><strong>stripepaymentpro/amd/src/stripe_payment_pro.js</strong>: Enhanced JavaScript display logic</li>
            <li><strong>stripepayment/externallib.php</strong>: Improved coupon lookup and validation</li>
        </ul>
    </div>
</body>
</html>
