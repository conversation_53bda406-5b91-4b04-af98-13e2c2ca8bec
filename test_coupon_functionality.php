<?php
/**
 * Test script for coupon functionality
 * 
 * This script tests the coupon application logic to ensure:
 * 1. Coupons can be applied using both name and ID
 * 2. Discounts are correctly calculated
 * 3. Display information is properly formatted
 * 
 * Usage: Run this script from the Moodle root directory
 * php enrol/test_coupon_functionality.php
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/enrol/stripepayment/externallib.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/externallib.php');

// Test configuration
$test_instance_id = 1; // Replace with actual instance ID
$test_coupon_id = 'test_coupon_id'; // Replace with actual Stripe coupon ID
$test_coupon_name = 'TEST10'; // Replace with actual coupon name

echo "=== Coupon Functionality Test ===\n\n";

// Test 1: Apply coupon by ID (stripepayment)
echo "Test 1: Apply coupon by ID (stripepayment)\n";
try {
    $result = moodle_enrol_stripepayment_external::stripepayment_applycoupon($test_coupon_id, $test_instance_id);
    echo "✓ Success: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

// Test 2: Apply coupon by name (stripepayment)
echo "Test 2: Apply coupon by name (stripepayment)\n";
try {
    $result = moodle_enrol_stripepayment_external::stripepayment_applycoupon($test_coupon_name, $test_instance_id);
    echo "✓ Success: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

// Test 3: Apply coupon by ID (stripepaymentpro)
echo "Test 3: Apply coupon by ID (stripepaymentpro)\n";
try {
    $result = moodle_enrol_stripepaymentpro_external::stripepaymentpro_applycoupon($test_coupon_id, $test_instance_id);
    echo "✓ Success: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

// Test 4: Apply coupon by name (stripepaymentpro)
echo "Test 4: Apply coupon by name (stripepaymentpro)\n";
try {
    $result = moodle_enrol_stripepaymentpro_external::stripepaymentpro_applycoupon($test_coupon_name, $test_instance_id);
    echo "✓ Success: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

// Test 5: Invalid coupon
echo "Test 5: Invalid coupon\n";
try {
    $result = moodle_enrol_stripepayment_external::stripepayment_applycoupon('invalid_coupon', $test_instance_id);
    echo "✗ Unexpected success: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "✓ Expected error: " . $e->getMessage() . "\n\n";
}

echo "=== Test Complete ===\n";
echo "\nTo run this test:\n";
echo "1. Update the test configuration variables at the top of this file\n";
echo "2. Ensure you have valid Stripe API keys configured\n";
echo "3. Create test coupons in your Stripe dashboard\n";
echo "4. Run: php enrol/test_coupon_functionality.php\n";
