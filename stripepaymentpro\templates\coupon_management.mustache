{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/coupon_management

    Template for coupon management interface

    Context variables required for this template:
    * coupons - Array of coupon data grouped by course
    * has_coupons - Boolean indicating if there are any coupons

    Example context (json):
    {
        "coupons": [
            {
                "course_name": "Course Name",
                "course_id": "prod_123",
                "has_multiple_coupons": true,
                "coupons": [
                    {
                        "coupon_id": "coupon_123",
                        "coupon_name": "DISCOUNT10",
                        "discount_text": "10% off",
                        "duration_text": " forever",
                        "expiry_text": ". Expiry: Never",
                        "full_description": "10% off forever. Expiry: Never"
                    }
                ]
            }
        ],
        "has_coupons": true
    }
}}

<!-- Navigation tabs -->
<nav class='strip-coupon-navigation-section' style='margin-bottom: 20px;'>
    <button id="all_coupon_button" style='margin-right: 10px;'>{{#str}}all_coupons, enrol_stripepaymentpro{{/str}}</button>
    <button id="generate_coupons_button">{{#str}}generate_coupons, enrol_stripepaymentpro{{/str}}</button>
</nav>

<!-- Display table of courses and associated coupons -->
<section class='all-coupons-section' id='all_coupons_section' style='display: block;'>
    {{#has_coupons}}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{{#str}}course_name, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}coupon_name, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}coupon_id, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}discount_amount, enrol_stripepaymentpro{{/str}}</th>
                    <th>{{#str}}actions, enrol_stripepaymentpro{{/str}}</th>
                </tr>
            </thead>
            <tbody>
                {{#coupons}}
                {{#coupons}}
                <tr class="coupon-row">
                    {{#is_first_coupon}}
                    <td rowspan="{{total_coupons}}" style="vertical-align: top; border-right: 2px solid #dee2e6; background-color: #f8f9fa; font-weight: bold; padding: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                            <div>
                                <i class="fa fa-graduation-cap mr-2"></i>{{course_name}}
                                <br><small class="text-muted">{{total_coupons}} coupon(s)</small>
                            </div>
                            <button class="btn btn-sm btn-warning deactivate-all-btn"
                                    data-course-id="{{course_id}}"
                                    title="Delete all coupons for this course"
                                    style="font-size: 0.7em; margin-left: 8px;"
                                    onclick="handleDeleteAllCoupons('{{course_id}}', this)">
                                <i class="fa fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                    {{/is_first_coupon}}
                    <td>
                        <strong>{{coupon_name}}</strong>
                    </td>
                    <td>
                        <button class="copy-coupon-id btn btn-sm btn-outline-secondary"
                                data-coupon-id="{{coupon_id}}"
                                title="Click to copy coupon ID"
                                style="font-family: monospace; padding: 4px 8px;">
                            {{coupon_id}}
                            <i class="fa fa-copy ml-1" style="font-size: 0.8em;"></i>
                        </button>
                    </td>
                    <td>
                        <span class="badge badge-info">{{discount_text}}</span>
                        <span class="badge badge-secondary">{{duration_text}}</span>
                        {{#expiry_text}}
                        <br><small class="text-muted">{{expiry_text}}</small>
                        {{/expiry_text}}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger deactivate-coupon-btn"
                                data-course-id="{{course_id}}"
                                data-coupon-id="{{coupon_id}}"
                                title="Deactivate this coupon"
                                onclick="handleCouponDelete('{{course_id}}', '{{coupon_id}}', this)">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
                {{/coupons}}
                {{/coupons}}
            </tbody>
        </table>
    </div>
    {{/has_coupons}}
    {{^has_coupons}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i>
        {{#str}}no_coupon_found, enrol_stripepaymentpro{{/str}}
    </div>
    {{/has_coupons}}
</section>

<!-- Form section for generating coupons -->
<section class='generate-coupon-section' id='generate_coupon_section' style='display: none;'>
    <h3>{{#str}}create_new_coupon, enrol_stripepaymentpro{{/str}}</h3>
    <div id="coupon-form-container">
        <!-- Form will be rendered by PHP and inserted here -->
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const allCouponsButton = document.getElementById('all_coupon_button');
    const generateCouponsButton = document.getElementById('generate_coupons_button');
    const allCouponsSection = document.getElementById('all_coupons_section');
    const generateCouponsSection = document.getElementById('generate_coupon_section');

    // Show all coupons section by default
    function showAllCoupons() {
        allCouponsSection.style.display = 'block';
        generateCouponsSection.style.display = 'none';
        allCouponsButton.classList.remove('btn-secondary');
        allCouponsButton.classList.add('btn-primary', 'active');
        generateCouponsButton.classList.remove('btn-primary', 'active');
        generateCouponsButton.classList.add('btn-secondary');
    }

    // Show generate coupons section
    function showGenerateCoupons() {
        allCouponsSection.style.display = 'none';
        generateCouponsSection.style.display = 'block';
        generateCouponsButton.classList.remove('btn-secondary');
        generateCouponsButton.classList.add('btn-primary', 'active');
        allCouponsButton.classList.remove('btn-primary', 'active');
        allCouponsButton.classList.add('btn-secondary');
    }

    // Event listeners
    if (allCouponsButton) {
        allCouponsButton.addEventListener('click', showAllCoupons);
    }

    if (generateCouponsButton) {
        generateCouponsButton.addEventListener('click', showGenerateCoupons);
    }

    // Initialize with all coupons view
    showAllCoupons();
});

// Helper function to find the first row in a course group
function findFirstRowInCourseGroup(currentRow) {
    let row = currentRow;
    // Go backwards to find the row with rowspan (course name cell)
    while (row && !row.querySelector('td[rowspan]')) {
        row = row.previousElementSibling;
    }
    return row;
}

// Fallback function for coupon deletion using Moodle AJAX
async function handleCouponDelete(courseId, couponId, button) {
    console.log('Fallback delete function called:', { courseId, couponId, button });

    if (!confirm('Are you sure you want to delete this coupon? This will remove it from both the database and Stripe.')) {
        return;
    }

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Use Moodle's AJAX API
        require(['core/ajax'], function(ajax) {
            const request = {
                methodname: 'moodle_stripepaymentpro_deactivate_coupon',
                args: {
                    courseid: courseId,
                    couponid: couponId
                }
            };

            ajax.call([request])[0]
                .then(function(result) {
                    console.log('Delete result:', result);

                    if (result.success) {
                        const row = button.closest('tr');
                        const courseCell = row.querySelector('td[rowspan]');

                        // If this row has the course name cell (first coupon), handle rowspan
                        if (courseCell) {
                            const currentRowspan = parseInt(courseCell.getAttribute('rowspan'));
                            if (currentRowspan > 1) {
                                // Move course cell to next row and decrease rowspan
                                const nextRow = row.nextElementSibling;
                                if (nextRow) {
                                    courseCell.setAttribute('rowspan', currentRowspan - 1);
                                    nextRow.insertBefore(courseCell, nextRow.firstChild);
                                }
                            }
                        } else {
                            // This is not the first row, just decrease rowspan of course cell
                            const firstRowInGroup = findFirstRowInCourseGroup(row);
                            if (firstRowInGroup) {
                                const courseCellInGroup = firstRowInGroup.querySelector('td[rowspan]');
                                if (courseCellInGroup) {
                                    const currentRowspan = parseInt(courseCellInGroup.getAttribute('rowspan'));
                                    courseCellInGroup.setAttribute('rowspan', currentRowspan - 1);
                                }
                            }
                        }

                        // Remove the row
                        row.remove();
                    } else {
                        button.innerHTML = originalHTML;
                        button.disabled = false;
                        alert('Error deleting coupon: ' + (result.message || 'Unknown error'));
                    }
                })
                .catch(function(error) {
                    console.error('Error:', error);
                    button.innerHTML = originalHTML;
                    button.disabled = false;
                    alert('Error deleting coupon: ' + error.message);
                });
        });

    } catch (error) {
        console.error('Error:', error);
        button.innerHTML = '<i class="fa fa-trash"></i>';
        button.disabled = false;
        alert('Error deleting coupon: ' + error.message);
    }
}

// Function to handle delete all coupons for a course
async function handleDeleteAllCoupons(courseId, button) {
    console.log('Delete all coupons called for course:', courseId);

    if (!confirm('Are you sure you want to delete ALL coupons for this course? This will remove them from both the database and Stripe.')) {
        return;
    }

    try {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Use Moodle's AJAX API
        require(['core/ajax'], function(ajax) {
            const request = {
                methodname: 'moodle_stripepaymentpro_deactivate_all_coupons',
                args: {
                    courseid: courseId
                }
            };

            ajax.call([request])[0]
                .then(function(result) {
                    console.log('Delete all result:', result);

                    if (result.success) {
                        alert('Success: ' + result.message);
                        window.location.reload();
                    } else {
                        button.innerHTML = originalHTML;
                        button.disabled = false;
                        alert('Error: ' + (result.message || 'Unknown error'));
                    }
                })
                .catch(function(error) {
                    console.error('Error:', error);
                    button.innerHTML = originalHTML;
                    button.disabled = false;
                    alert('Error deleting coupons: ' + error.message);
                });
        });

    } catch (error) {
        console.error('Error:', error);
        button.innerHTML = '<i class="fa fa-trash-alt"></i>';
        button.disabled = false;
        alert('Error deleting coupons: ' + error.message);
    }
}
</script>
