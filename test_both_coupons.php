<?php
/**
 * Test script for both coupon scenarios
 * 
 * Scenario 1: 7% off forever
 * - Subtotal: $120 ($70 + $50)
 * - 7% discount: $120 × 0.07 = $8.40
 * - Expected total: $120 - $8.40 = $111.60
 * 
 * Scenario 2: $20 off for 2 months
 * - Subtotal: $120 ($70 + $50)
 * - $20 discount: $20.00
 * - Expected total: $120 - $20 = $100.00
 */

echo "=== Coupon Calculation Test ===\n\n";

// Common values
$sign_up_fee = 70.00;
$renewal_fee = 50.00;
$subtotal = $sign_up_fee + $renewal_fee; // $120

echo "Base pricing:\n";
echo "- Sign-up fee (initial): $" . number_format($sign_up_fee, 2) . "\n";
echo "- Renewal fee (recurring): $" . number_format($renewal_fee, 2) . "\n";
echo "- Subtotal: $" . number_format($subtotal, 2) . "\n\n";

// Test 1: 7% off forever
echo "=== Test 1: 7% off forever (kk coupon) ===\n";
$percent_off = 7;
$discount_amount_1 = $subtotal * ($percent_off / 100);
$total_1 = $subtotal - $discount_amount_1;

echo "Calculation:\n";
echo "- Discount: $" . number_format($subtotal, 2) . " × {$percent_off}% = $" . number_format($discount_amount_1, 2) . "\n";
echo "- Total due today: $" . number_format($subtotal, 2) . " - $" . number_format($discount_amount_1, 2) . " = $" . number_format($total_1, 2) . "\n";
echo "- Expected (Stripe): $111.60\n";
echo "- Our result: $" . number_format($total_1, 2) . "\n";
echo "- Match: " . (abs($total_1 - 111.60) < 0.01 ? "✅ YES" : "❌ NO") . "\n\n";

// Test 2: $20 off for 2 months
echo "=== Test 2: $20 off for 2 months (jjk coupon) ===\n";
$amount_off = 20.00;
$discount_amount_2 = min($amount_off, $subtotal); // Don't exceed subtotal
$total_2 = $subtotal - $discount_amount_2;

echo "Calculation:\n";
echo "- Discount: $" . number_format($amount_off, 2) . " (fixed amount)\n";
echo "- Total due today: $" . number_format($subtotal, 2) . " - $" . number_format($discount_amount_2, 2) . " = $" . number_format($total_2, 2) . "\n";
echo "- Expected (Stripe): $100.00\n";
echo "- Our result: $" . number_format($total_2, 2) . "\n";
echo "- Match: " . (abs($total_2 - 100.00) < 0.01 ? "✅ YES" : "❌ NO") . "\n\n";

// Compare with old (incorrect) calculation
echo "=== Comparison with Old (Incorrect) Calculation ===\n";
echo "Old method (discount applied to $70 only):\n";

// Old calculation for 7%
$old_discount_1 = $sign_up_fee * ($percent_off / 100);
$old_total_1 = $sign_up_fee - $old_discount_1;
echo "- 7% off: $" . number_format($sign_up_fee, 2) . " × 7% = $" . number_format($old_discount_1, 2) . " discount\n";
echo "- Old total: $" . number_format($old_total_1, 2) . " (WRONG - was showing $65.10)\n";

// Old calculation for $20
$old_discount_2 = min($amount_off, $sign_up_fee);
$old_total_2 = $sign_up_fee - $old_discount_2;
echo "- $20 off: $" . number_format($sign_up_fee, 2) . " - $" . number_format($old_discount_2, 2) . " = $" . number_format($old_total_2, 2) . "\n";
echo "- Old total: $" . number_format($old_total_2, 2) . " (WRONG - was showing $50.00)\n\n";

echo "=== Summary ===\n";
echo "✅ NEW METHOD (correct):\n";
echo "   - Apply discount to full subtotal ($120)\n";
echo "   - 7% off: $111.60 (matches Stripe)\n";
echo "   - $20 off: $100.00 (matches Stripe)\n\n";
echo "❌ OLD METHOD (incorrect):\n";
echo "   - Applied discount only to initial fee ($70)\n";
echo "   - 7% off: $65.10 (wrong)\n";
echo "   - $20 off: $50.00 (wrong)\n\n";
echo "🎯 The key fix: Calculate discount based on subtotal ($120), not just initial fee ($70)\n";
?>
